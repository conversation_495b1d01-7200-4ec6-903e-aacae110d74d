// Main application logic
class LazyRemixer {
    constructor() {
        this.fileManager = new FileManager();
        this.videoProcessor = new VideoProcessor();
        this.generatedVideos = [];
        this.downloadDestination = null; // Store the selected download folder

        this.initializeElements();
        this.bindEvents();
        this.checkBrowserSupport();
    }

    // Initialize DOM elements
    initializeElements() {
        this.elements = {
            selectFolderBtn: document.getElementById('selectFolderBtn'),
            folderInfo: document.getElementById('folderInfo'),
            folderPath: document.getElementById('folderPath'),
            videoCount: document.getElementById('videoCount'),
            videoList: document.getElementById('videoList'),
            selectDestinationBtn: document.getElementById('selectDestinationBtn'),
            destinationInfo: document.getElementById('destinationInfo'),
            destinationPath: document.getElementById('destinationPath'),
            duration: document.getElementById('duration'),
            quantity: document.getElementById('quantity'),
            testFFmpegBtn: document.getElementById('testFFmpegBtn'),
            generateBtn: document.getElementById('generateBtn'),
            progressSection: document.getElementById('progressSection'),
            progressFill: document.getElementById('progressFill'),
            progressText: document.getElementById('progressText'),
            downloadSection: document.getElementById('downloadSection')
        };
    }

    // Bind event listeners
    bindEvents() {
        this.elements.selectFolderBtn.addEventListener('click', () => this.selectFolder());
        this.elements.selectDestinationBtn.addEventListener('click', () => this.selectDestination());
        this.elements.testFFmpegBtn.addEventListener('click', () => this.testFFmpeg());
        this.elements.generateBtn.addEventListener('click', () => this.generateVideos());

        // Enable generate button when folder is selected and inputs are valid
        [this.elements.duration, this.elements.quantity].forEach(input => {
            input.addEventListener('input', () => this.validateInputs());
        });
    }

    // Check browser support
    checkBrowserSupport() {
        if (!this.fileManager.isSupported()) {
            this.showError('Your browser does not support the File System Access API. Please use Chrome 86+ or Edge 86+.');
            this.elements.selectFolderBtn.disabled = true;
            this.elements.selectDestinationBtn.disabled = true;
        } else {
            console.log('File System Access API supported');
        }

        // Server-side processing is always available
        console.log('Server-side video processing ready');
    }

    // Select video folder
    async selectFolder() {
        try {
            this.elements.selectFolderBtn.disabled = true;
            this.elements.selectFolderBtn.textContent = 'Selecting...';

            const folderInfo = await this.fileManager.selectFolder();

            this.displayFolderInfo(folderInfo);
            this.validateInputs();

            this.showSuccess(`Found ${folderInfo.videoCount} video files in "${folderInfo.name}"`);
        } catch (error) {
            this.showError(error.message);
        } finally {
            this.elements.selectFolderBtn.disabled = false;
            this.elements.selectFolderBtn.textContent = 'Choose Video Folder';
        }
    }

    // Select download destination folder
    async selectDestination() {
        try {
            this.elements.selectDestinationBtn.disabled = true;
            this.elements.selectDestinationBtn.textContent = 'Selecting...';

            // Use File System Access API to select a writable directory
            this.downloadDestination = await window.showDirectoryPicker();

            this.displayDestinationInfo();
            this.validateInputs();

            this.showSuccess(`Download destination set to "${this.downloadDestination.name}"`);
        } catch (error) {
            if (error.name === 'AbortError') {
                // User cancelled the selection
                console.log('Destination selection cancelled');
            } else {
                this.showError(`Failed to select destination: ${error.message}`);
            }
        } finally {
            this.elements.selectDestinationBtn.disabled = false;
            this.elements.selectDestinationBtn.textContent = 'Choose Download Folder';
        }
    }

    // Display folder information
    displayFolderInfo(folderInfo) {
        this.elements.folderPath.textContent = folderInfo.name;
        this.elements.videoCount.textContent = folderInfo.videoCount;

        // Display video list
        this.elements.videoList.innerHTML = '';
        folderInfo.videos.forEach(video => {
            const videoItem = document.createElement('div');
            videoItem.className = 'video-item';
            videoItem.textContent = `${video.name} (${video.extension})`;
            this.elements.videoList.appendChild(videoItem);
        });

        this.elements.folderInfo.style.display = 'block';
    }

    // Display destination information
    displayDestinationInfo() {
        this.elements.destinationPath.textContent = this.downloadDestination.name;
        this.elements.destinationInfo.style.display = 'block';
    }

    // Test server connectivity and video processing
    async testFFmpeg() {
        try {
            this.elements.testFFmpegBtn.disabled = true;
            this.elements.testFFmpegBtn.textContent = 'Testing...';
            this.showProgress(0, 'Testing server connectivity...');

            console.log('Starting server test...');

            // Check VideoProcessor version
            const status = this.videoProcessor.getStatus();
            console.log('VideoProcessor status:', status);

            if (!this.videoProcessor.isServerSideVersion()) {
                throw new Error('Wrong VideoProcessor version detected. Please refresh the page to clear cache.');
            }

            // Test server connectivity
            this.showProgress(50, 'Checking server status...');
            const response = await fetch('/api/cleanup', { method: 'DELETE' });

            if (!response.ok) {
                throw new Error(`Server not responding: ${response.statusText}`);
            }

            this.showProgress(100, 'Server test complete!');
            this.hideProgress();
            this.showSuccess('Server is working correctly! You can now generate videos.');
            console.log('Server test completed successfully');
        } catch (error) {
            console.error('Server test failed:', error);
            this.hideProgress();
            this.showError(`Server test failed: ${error.message}. Make sure the server is running.`);
        } finally {
            this.elements.testFFmpegBtn.disabled = false;
            this.elements.testFFmpegBtn.textContent = 'Test Server';
        }
    }

    // Validate inputs and enable/disable generate button
    validateInputs() {
        const duration = parseInt(this.elements.duration.value);
        const quantity = parseInt(this.elements.quantity.value);
        const hasVideos = this.fileManager.videoFiles.length > 0;
        const hasDestination = this.downloadDestination !== null;

        const isValid = hasVideos &&
                       hasDestination &&
                       duration >= 10 && duration <= 600 &&
                       quantity >= 1 && quantity <= 10;

        this.elements.generateBtn.disabled = !isValid;

        // Update button text to show what's missing
        if (!hasVideos && !hasDestination) {
            this.elements.generateBtn.textContent = 'Select Video Folder & Download Destination';
        } else if (!hasVideos) {
            this.elements.generateBtn.textContent = 'Select Video Folder First';
        } else if (!hasDestination) {
            this.elements.generateBtn.textContent = 'Select Download Destination First';
        } else if (!isValid) {
            this.elements.generateBtn.textContent = 'Check Duration & Quantity Settings';
        } else {
            this.elements.generateBtn.textContent = 'Generate Random Videos';
        }
    }

    // Generate random videos with immediate downloads
    async generateVideos() {
        console.log('Generate button clicked!'); // Debug log
        try {
            const duration = parseInt(this.elements.duration.value);
            const quantity = parseInt(this.elements.quantity.value);

            console.log('Duration:', duration, 'Quantity:', quantity); // Debug log
            console.log('Video files count:', this.fileManager.videoFiles.length); // Debug log

            if (this.fileManager.videoFiles.length === 0) {
                throw new Error('No video files selected');
            }

            this.elements.generateBtn.disabled = true;
            this.elements.generateBtn.textContent = 'Generating...';
            this.showProgress(0, 'Initializing...');

            // Clear previous downloads
            this.generatedVideos = [];
            this.elements.downloadSection.innerHTML = '<p class="placeholder">Generated videos will appear here for download</p>';

            console.log('About to initialize video processor...'); // Debug log

            // Initialize video processor if not already done
            if (!this.videoProcessor.isLoaded) {
                console.log('Loading FFmpeg...'); // Debug log
                await this.videoProcessor.initialize((progress, message) => {
                    console.log('FFmpeg loading progress:', progress, message); // Debug log
                    this.showProgress(progress * 0.1, `Loading FFmpeg: ${message}`);
                });
                console.log('FFmpeg loaded successfully'); // Debug log
            }

            console.log('Starting video generation...'); // Debug log

            // Generate videos with immediate download callback
            const results = await this.videoProcessor.generateRandomVideos(
                this.fileManager.videoFiles,
                duration,
                quantity,
                (progress, message) => {
                    console.log('Generation progress:', progress, message); // Debug log
                    this.showProgress(progress, message);
                },
                (video, videoNumber) => {
                    console.log('Video ready:', videoNumber); // Debug log
                    this.onVideoReady(video, videoNumber);
                }
            );

            this.generatedVideos = results;
            this.hideProgress();

            this.showSuccess(`Successfully generated and downloaded ${results.length} video(s)!`);
        } catch (error) {
            console.error('Error in generateVideos:', error); // Debug log
            this.showError(error.message);
            this.hideProgress();
        } finally {
            this.elements.generateBtn.disabled = false;
            this.elements.generateBtn.textContent = 'Generate Random Videos';
        }
    }

    // Show progress
    showProgress(percentage, message) {
        this.elements.progressSection.style.display = 'block';
        this.elements.progressFill.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
        this.elements.progressText.textContent = message;
    }

    // Hide progress
    hideProgress() {
        this.elements.progressSection.style.display = 'none';
    }

    // Display download links (now only used for batch display if needed)
    displayDownloads(videos) {
        // This method is now mainly for compatibility
        // Individual videos are added via addDownloadItem as they're ready
        if (this.elements.downloadSection.innerHTML.includes('placeholder')) {
            this.elements.downloadSection.innerHTML = '';
        }
    }

    // Handle video ready for immediate download
    async onVideoReady(video, videoNumber) {
        console.log('onVideoReady called with video:', video, 'videoNumber:', videoNumber);

        // Clear placeholder if this is the first video
        if (this.generatedVideos.length === 0) {
            this.elements.downloadSection.innerHTML = '';
        }

        try {
            // Download video data from server first
            console.log('Downloading video blob for:', video.filename);
            const videoData = await this.videoProcessor.downloadVideoBlob(video.filename);
            const videoWithData = { ...video, data: videoData };

            console.log('Video data downloaded, size:', videoData.byteLength);

            // Add to download section with complete data
            this.addDownloadItem(videoWithData, videoNumber);

            // Automatically download the video
            await this.downloadVideo(videoWithData, videoNumber);
            this.showSuccess(`Video ${videoNumber} ready and saved!`);
        } catch (error) {
            console.error('Error in onVideoReady:', error);
            // Add item even if download fails, but without data
            this.addDownloadItem(video, videoNumber);
            this.showError(`Failed to save video ${videoNumber}: ${error.message}`);
        }
    }

    // Add download item to the UI
    addDownloadItem(video, videoNumber) {
        const downloadItem = document.createElement('div');
        downloadItem.className = 'download-item';

        const info = document.createElement('div');
        info.className = 'download-info';

        const title = document.createElement('h4');
        title.textContent = `Random Video ${videoNumber}`;

        const details = document.createElement('p');
        const fileSize = video.data ? this.formatFileSize(video.data.byteLength || video.data.length) :
                         video.size ? this.formatFileSize(video.size) : 'Processing...';
        const duration = video.duration ? Math.round(video.duration) : 'Unknown';
        const clipCount = video.clips ? video.clips.length : 0;
        details.textContent = `Duration: ${duration}s | Clips: ${clipCount} | Size: ${fileSize}`;

        const clipsList = document.createElement('p');
        clipsList.textContent = `Clips used: ${video.clips ? video.clips.join(', ') : 'Processing...'}`;
        clipsList.style.fontSize = '0.8em';
        clipsList.style.color = '#718096';

        const status = document.createElement('p');
        status.textContent = '✅ Downloaded automatically';
        status.style.fontSize = '0.9em';
        status.style.color = '#38a169';
        status.style.fontWeight = '600';

        info.appendChild(title);
        info.appendChild(details);
        info.appendChild(clipsList);
        info.appendChild(status);

        const downloadBtn = document.createElement('button');
        downloadBtn.className = 'btn btn-download';
        downloadBtn.textContent = 'Save Again';
        downloadBtn.onclick = async () => {
            try {
                downloadBtn.disabled = true;
                downloadBtn.textContent = 'Downloading...';

                // Download video data from server if not already available
                let videoWithData = video;
                if (!video.data) {
                    const videoData = await this.videoProcessor.downloadVideoBlob(video.filename);
                    videoWithData = { ...video, data: videoData };
                }
                await this.downloadVideo(videoWithData, videoNumber);
                this.showSuccess(`Video ${videoNumber} saved again!`);
            } catch (error) {
                console.error('Download error:', error);
                this.showError(`Failed to save video: ${error.message}`);
            } finally {
                downloadBtn.disabled = false;
                downloadBtn.textContent = 'Save Again';
            }
        };

        downloadItem.appendChild(info);
        downloadItem.appendChild(downloadBtn);
        this.elements.downloadSection.appendChild(downloadItem);
    }

    // Download video to selected destination
    async downloadVideo(video, number) {
        try {
            const fileName = `LazyRemix_${number}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.mp4`;

            if (this.downloadDestination) {
                // Save directly to the selected folder using File System Access API
                const fileHandle = await this.downloadDestination.getFileHandle(fileName, { create: true });
                const writable = await fileHandle.createWritable();

                // Write the video data
                await writable.write(video.data);
                await writable.close();

                console.log(`Video saved to: ${this.downloadDestination.name}/${fileName}`);
            } else {
                // Fallback to browser download
                const blob = this.videoProcessor.createDownloadBlob(video.data);
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);

                // Clean up the URL object
                setTimeout(() => URL.revokeObjectURL(url), 1000);
            }
        } catch (error) {
            console.error('Error downloading video:', error);
            this.showError(`Failed to save video: ${error.message}`);
        }
    }

    // Format file size
    formatFileSize(bytes) {
        return this.fileManager.formatFileSize(bytes);
    }

    // Show error message
    showError(message) {
        this.removeMessages();
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error';
        errorDiv.textContent = message;
        document.querySelector('.container').insertBefore(errorDiv, document.querySelector('main'));
        
        // Auto-remove after 5 seconds
        setTimeout(() => this.removeMessages(), 5000);
    }

    // Show success message
    showSuccess(message) {
        this.removeMessages();
        const successDiv = document.createElement('div');
        successDiv.className = 'success';
        successDiv.textContent = message;
        document.querySelector('.container').insertBefore(successDiv, document.querySelector('main'));
        
        // Auto-remove after 3 seconds
        setTimeout(() => this.removeMessages(), 3000);
    }

    // Remove all messages
    removeMessages() {
        document.querySelectorAll('.error, .success').forEach(el => el.remove());
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new LazyRemixer();
});
