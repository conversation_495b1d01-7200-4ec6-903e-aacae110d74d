<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LazyRemixer Version Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background-color: #1e3a1e;
            border-color: #4caf50;
        }
        .error {
            background-color: #3a1e1e;
            border-color: #f44336;
        }
        .info {
            background-color: #1e2a3a;
            border-color: #2196f3;
        }
        button {
            background-color: #4caf50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #666;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>🔧 LazyRemixer Version Test</h1>
    <p>This page tests if the correct server-side version of VideoProcessor is loaded.</p>
    
    <button id="testBtn" onclick="runTests()">Run Tests</button>
    <button onclick="clearCache()">Clear Cache & Reload</button>
    
    <div id="results"></div>

    <script src="video-processor.js?v=2.0.0"></script>
    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function runTests() {
            clearResults();
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = 'Running Tests...';

            try {
                addResult('🔍 Testing VideoProcessor version...', 'info');

                // Test 1: Check if VideoProcessor exists
                if (typeof VideoProcessor === 'undefined') {
                    addResult('❌ FAIL: VideoProcessor class not found', 'error');
                    return;
                }
                addResult('✅ PASS: VideoProcessor class found', 'success');

                // Test 2: Create instance
                const processor = new VideoProcessor();
                addResult('✅ PASS: VideoProcessor instance created', 'success');

                // Test 3: Check version
                const status = processor.getStatus();
                addResult(`📊 Status: ${JSON.stringify(status, null, 2)}`, 'info');

                if (status.version === '2.0.0') {
                    addResult('✅ PASS: Correct version (2.0.0) detected', 'success');
                } else {
                    addResult(`❌ FAIL: Wrong version detected: ${status.version}`, 'error');
                }

                // Test 4: Check if server-side version
                if (processor.isServerSideVersion && processor.isServerSideVersion()) {
                    addResult('✅ PASS: Server-side version confirmed', 'success');
                } else {
                    addResult('❌ FAIL: Not server-side version', 'error');
                }

                // Test 5: Test server connectivity
                addResult('🌐 Testing server connectivity...', 'info');
                try {
                    await processor.initialize();
                    addResult('✅ PASS: Server connectivity test successful', 'success');
                } catch (error) {
                    addResult(`❌ FAIL: Server connectivity failed: ${error.message}`, 'error');
                }

                addResult('🎉 All tests completed!', 'success');

            } catch (error) {
                addResult(`❌ CRITICAL ERROR: ${error.message}`, 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = 'Run Tests';
            }
        }

        function clearCache() {
            // Clear cache and reload
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            // Force reload with cache bypass
            window.location.reload(true);
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
