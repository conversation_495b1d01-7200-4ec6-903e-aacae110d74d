* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #e2e8f0;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(26, 32, 44, 0.95);
    margin-top: 20px;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #eee;
}

header h1 {
    font-size: 2.5em;
    color: #f7fafc;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

header p {
    color: #a0aec0;
    font-size: 1.1em;
}

.section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(45, 55, 72, 0.6);
    border-radius: 8px;
    border-left: 4px solid #667eea;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.section h2 {
    color: #f7fafc;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #5a6fd8, #6b46c1);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #38a169, #2f855a);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
}

.info-box {
    margin-top: 15px;
    padding: 15px;
    background: rgba(26, 32, 44, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-box p {
    margin-bottom: 8px;
    color: #e2e8f0;
}

.video-list {
    max-height: 200px;
    overflow-y: auto;
    margin-top: 10px;
    padding: 10px;
    background: rgba(26, 32, 44, 0.6);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.video-item {
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9em;
    color: #cbd5e0;
}

.video-item:last-child {
    border-bottom: none;
}

.settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

@media (max-width: 600px) {
    .settings-grid {
        grid-template-columns: 1fr;
    }
}

.setting-item {
    display: flex;
    flex-direction: column;
}

.setting-item label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #f7fafc;
}

.setting-item input {
    padding: 10px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    background: rgba(26, 32, 44, 0.8);
    color: #e2e8f0;
}

.setting-item input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.setting-item small {
    margin-top: 5px;
    color: #a0aec0;
    font-size: 0.85em;
}

.progress-section {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: rgba(26, 32, 44, 0.8);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #48bb78, #38a169);
    width: 0%;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(72, 187, 120, 0.3);
}

#progressText {
    text-align: center;
    color: #e2e8f0;
    font-weight: 600;
}

.download-section {
    min-height: 100px;
}

.placeholder {
    text-align: center;
    color: #718096;
    font-style: italic;
    padding: 40px 20px;
}

.download-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(26, 32, 44, 0.8);
    border-radius: 6px;
    margin-bottom: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.download-item:last-child {
    margin-bottom: 0;
}

.download-info {
    flex: 1;
}

.download-info h4 {
    color: #f7fafc;
    margin-bottom: 5px;
}

.download-info p {
    color: #a0aec0;
    font-size: 0.9em;
}

.btn-download {
    background: linear-gradient(135deg, #ed8936, #dd7724);
    color: white;
    padding: 8px 16px;
    font-size: 14px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-download:hover {
    background: linear-gradient(135deg, #dd7724, #c05621);
    box-shadow: 0 4px 12px rgba(237, 137, 54, 0.4);
}

footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    color: #a0aec0;
    font-size: 0.9em;
}

.error {
    background: rgba(254, 215, 215, 0.1);
    color: #fc8181;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    border-left: 4px solid #e53e3e;
    border: 1px solid rgba(229, 62, 62, 0.3);
}

.success {
    background: rgba(198, 246, 213, 0.1);
    color: #68d391;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    border-left: 4px solid #38a169;
    border: 1px solid rgba(56, 161, 105, 0.3);
}
