# LazyRemixer 🎬

A web application that randomly combines video files from your local computer into new creative videos.

## Features

- 📁 **Local File Access**: Browse and select video folders directly from your computer
- 🎯 **Random Combination**: Automatically combines random video clips to match your desired duration
- ⚙️ **Customizable Settings**: Set target duration (10-600 seconds) and number of videos to generate (1-10)
- 🎬 **Multiple Formats**: Supports MP4, AVI, MOV, MKV, and WebM video files
- 📥 **Instant Downloads**: Videos download automatically as soon as they're ready (no waiting for batch completion)
- 🔄 **Progress Tracking**: Real-time progress updates during video processing
- 🌐 **Browser-Based**: No installation required - runs entirely in your web browser
- 🐳 **Docker Ready**: Easy deployment with Docker and Docker Compose

## Requirements

- **Modern Browser**: Chrome 86+ or Edge 86+ (required for File System Access API)
- **Node.js**: Version 14 or higher (for running the local server)

## Quick Start

### Option 1: Docker (Recommended for Easy Deployment)

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd LazyRemixer
   ```

2. **Run with Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Open your browser**
   Navigate to `http://localhost:3000`

### Option 2: Local Development

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd LazyRemixer
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the server**
   ```bash
   npm start
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

### Using the App

1. **Start remixing!**
   - Click "Choose Video Folder" to select a folder containing your video files
   - Set your desired video duration and quantity
   - Click "Generate Random Videos"
   - Videos will automatically download as they're completed!

## How It Works

1. **File Selection**: Uses the File System Access API to browse your local file system
2. **Video Processing**: Leverages FFmpeg.wasm for client-side video processing
3. **Random Combination**: Selects random clips from your videos and combines them
4. **Download**: Generates downloadable MP4 files

## Supported Video Formats

- MP4 (.mp4)
- AVI (.avi)
- MOV (.mov)
- MKV (.mkv)
- WebM (.webm)

## Settings

- **Target Duration**: 10-600 seconds (10 minutes maximum)
- **Number of Videos**: 1-10 videos per generation
- **Clip Length**: Random clips between 5-30 seconds each

## Browser Compatibility

This app requires browsers that support the File System Access API:

- ✅ Chrome 86+
- ✅ Edge 86+
- ❌ Firefox (not supported)
- ❌ Safari (not supported)

## Technical Details

- **Frontend**: Vanilla JavaScript, HTML5, CSS3
- **Video Processing**: FFmpeg.wasm (WebAssembly)
- **File Access**: File System Access API
- **Server**: Express.js (for local development)

## Troubleshooting

### "File System Access API not supported"
- Make sure you're using Chrome 86+ or Edge 86+
- The app won't work in Firefox or Safari

### "Failed to load FFmpeg"
- Check your internet connection (FFmpeg.wasm is loaded from CDN)
- Try refreshing the page

### Videos won't process
- Ensure your video files are in supported formats
- Check that the files aren't corrupted
- Try with smaller video files first

### Slow processing
- Video processing happens in your browser and can be CPU-intensive
- Larger videos and longer durations will take more time
- Consider reducing the target duration or number of videos

## Docker Deployment

### Quick Docker Commands

```bash
# Build and run with Docker Compose (recommended)
docker-compose up -d

# Stop the application
docker-compose down

# View logs
docker-compose logs -f

# Rebuild after changes
docker-compose up -d --build
```

### Manual Docker Commands

```bash
# Build the Docker image
npm run docker:build

# Run the container
npm run docker:run

# Or use docker directly
docker build -t lazy-remixer .
docker run -p 3000:3000 lazy-remixer
```

### Production Deployment

For production deployment, you can:

1. **Deploy to any Docker-compatible platform** (AWS ECS, Google Cloud Run, Azure Container Instances, etc.)
2. **Use Docker Swarm** for multi-node deployment
3. **Deploy to Kubernetes** using the provided Docker image

The app will be available at `http://localhost:3000` or your server's IP address.

## Development

To modify or extend the app:

1. **File Structure**:
   - `index.html` - Main web interface
   - `style.css` - Styling and layout
   - `script.js` - Main application logic
   - `file-manager.js` - File system access utilities
   - `video-processor.js` - Video processing with FFmpeg.wasm
   - `server.js` - Local development server

2. **Key Classes**:
   - `LazyRemixer` - Main application controller
   - `FileManager` - Handles file system access
   - `VideoProcessor` - Manages video processing with FFmpeg

3. **Adding Features**:
   - Modify `video-processor.js` to add new video effects
   - Update `file-manager.js` to support additional file formats
   - Extend `script.js` for new UI features

## License

MIT License - feel free to use and modify as needed!

## Contributing

Contributions are welcome! Please feel free to submit issues or pull requests.

---

**Note**: This app processes videos entirely in your browser - no files are uploaded to any server. Your privacy and data remain completely under your control.
