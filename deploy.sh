#!/bin/bash

# LazyRemixer Deployment Script

echo "🎬 LazyRemixer Deployment Script"
echo "================================"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

echo "✅ Docker and Docker Compose are available"

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down 2>/dev/null || true

# Build and start the application
echo "🔨 Building and starting LazyRemixer..."
docker-compose up -d --build

# Wait a moment for the container to start
echo "⏳ Waiting for application to start..."
sleep 5

# Check if the container is running
if docker-compose ps | grep -q "Up"; then
    echo "✅ LazyRemixer is now running!"
    echo "🌐 Open your browser and go to: http://localhost:3000"
    echo ""
    echo "📋 Useful commands:"
    echo "   View logs:     docker-compose logs -f"
    echo "   Stop app:      docker-compose down"
    echo "   Restart app:   docker-compose restart"
    echo ""
    echo "🎯 To use the app:"
    echo "   1. Open http://localhost:3000 in Chrome or Edge"
    echo "   2. Click 'Choose Video Folder' to select your videos"
    echo "   3. Set duration and quantity"
    echo "   4. Click 'Generate Random Videos'"
    echo "   5. Videos will download automatically as they're ready!"
else
    echo "❌ Failed to start LazyRemixer. Check the logs:"
    docker-compose logs
    exit 1
fi
