class VideoProcessor {
    constructor() {
        this.isLoaded = true; // Server-side processing is always "loaded"
        this.isProcessing = false;
        this.uploadedFiles = [];
        this.version = '2.0.0'; // Version for debugging
        console.log('VideoProcessor v2.0.0 - Server-side FFmpeg processing');
    }

    // Initialize is now just a compatibility method
    async initialize(onProgress) {
        try {
            console.log('Initializing VideoProcessor v2.0.0 (server-side mode)');

            // Test server connectivity
            const response = await fetch('/api/cleanup', { method: 'DELETE' });
            if (!response.ok) {
                throw new Error(`Server not responding: ${response.statusText}`);
            }

            if (onProgress) {
                onProgress(100, 'Server-side processing ready');
            }
            console.log('Video processor initialized successfully (server-side mode)');
            return Promise.resolve();
        } catch (error) {
            console.error('VideoProcessor initialization failed:', error);
            throw new Error(`Failed to initialize server-side video processing: ${error.message}`);
        }
    }

    // Upload video files to server
    async uploadVideoFiles(videoFiles, onProgress) {
        console.log('Uploading video files to server...');

        const formData = new FormData();

        // Add each video file to the form data
        for (let i = 0; i < videoFiles.length; i++) {
            const videoFile = videoFiles[i];
            const fileData = await this.getVideoFileData(videoFile);
            const blob = new Blob([fileData], { type: 'video/mp4' });
            formData.append('videos', blob, videoFile.name);

            if (onProgress) {
                const progress = ((i + 1) / videoFiles.length) * 100;
                onProgress(progress, `Uploading ${i + 1}/${videoFiles.length} files...`);
            }
        }

        try {
            const response = await fetch('/api/upload-videos', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`Upload failed: ${response.statusText}`);
            }

            const result = await response.json();
            this.uploadedFiles = result.files;

            console.log('Upload successful:', result);
            return result;
        } catch (error) {
            console.error('Upload error:', error);
            throw error;
        }
    }

    // Generate random video combinations with server-side processing
    async generateRandomVideos(videoFiles, targetDuration, quantity, onProgress, onVideoReady) {
        console.log('generateRandomVideos called with:', {
            videoFilesCount: videoFiles.length,
            targetDuration,
            quantity,
            isLoaded: this.isLoaded,
            isProcessing: this.isProcessing
        });

        if (this.isProcessing) {
            throw new Error('Already processing videos');
        }

        this.isProcessing = true;
        const results = [];

        try {
            // First upload the video files if not already uploaded
            if (this.uploadedFiles.length === 0) {
                if (onProgress) onProgress(0, 'Uploading video files...');
                await this.uploadVideoFiles(videoFiles, (progress, message) => {
                    if (onProgress) onProgress(progress * 0.3, message);
                });
            }

            if (onProgress) onProgress(30, 'Starting video generation...');

            // Start server-side generation with Server-Sent Events
            const response = await fetch('/api/generate-videos', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    files: this.uploadedFiles,
                    targetDuration,
                    quantity
                })
            });

            if (!response.ok) {
                throw new Error(`Generation failed: ${response.statusText}`);
            }

            // Handle Server-Sent Events
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));

                            if (data.type === 'progress' && onProgress) {
                                const overallProgress = 30 + (data.progress * 0.7);
                                onProgress(overallProgress, data.message);
                            } else if (data.type === 'video-ready' && onVideoReady) {
                                results.push(data.video);
                                await onVideoReady(data.video, data.videoNumber);
                            } else if (data.type === 'error') {
                                throw new Error(data.error);
                            } else if (data.type === 'complete') {
                                console.log('Generation complete:', data.message);
                            }
                        } catch (parseError) {
                            console.warn('Failed to parse SSE data:', parseError);
                        }
                    }
                }
            }

            return results;
        } finally {
            this.isProcessing = false;
        }
    }

    // Get video file data from File System Access API
    async getVideoFileData(videoFile) {
        try {
            const file = await videoFile.handle.getFile();
            return await file.arrayBuffer();
        } catch (error) {
            throw new Error(`Failed to read video file ${videoFile.name}: ${error.message}`);
        }
    }

    // Download video from server as blob
    async downloadVideoBlob(filename) {
        try {
            const response = await fetch(`/api/video-blob/${filename}`);
            if (!response.ok) {
                throw new Error(`Failed to fetch video: ${response.statusText}`);
            }
            return await response.arrayBuffer();
        } catch (error) {
            console.error('Error downloading video blob:', error);
            throw error;
        }
    }

    // Create download blob from video data
    createDownloadBlob(videoData) {
        return new Blob([videoData], { type: 'video/mp4' });
    }

    // Get processing status
    getStatus() {
        return {
            isLoaded: this.isLoaded,
            isProcessing: this.isProcessing
        };
    }

}

// Export for use in other modules
window.VideoProcessor = VideoProcessor;
