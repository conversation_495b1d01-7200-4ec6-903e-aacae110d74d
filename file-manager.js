class FileManager {
    constructor() {
        this.selectedFolder = null;
        this.videoFiles = [];
        this.supportedFormats = ['.mp4', '.avi', '.mov', '.mkv', '.webm'];
    }

    // Check if File System Access API is supported
    isSupported() {
        return 'showDirectoryPicker' in window;
    }

    // Select a folder using File System Access API
    async selectFolder() {
        if (!this.isSupported()) {
            throw new Error('File System Access API is not supported in this browser. Please use Chrome 86+ or Edge 86+.');
        }

        try {
            this.selectedFolder = await window.showDirectoryPicker();
            await this.scanVideoFiles();
            return {
                name: this.selectedFolder.name,
                videoCount: this.videoFiles.length,
                videos: this.videoFiles
            };
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('Folder selection was cancelled.');
            }
            throw new Error(`Failed to select folder: ${error.message}`);
        }
    }

    // Scan the selected folder for video files
    async scanVideoFiles() {
        if (!this.selectedFolder) {
            throw new Error('No folder selected');
        }

        this.videoFiles = [];
        await this.scanDirectory(this.selectedFolder, '');
    }

    // Recursively scan directory for video files
    async scanDirectory(directoryHandle, path) {
        try {
            for await (const entry of directoryHandle.values()) {
                const currentPath = path ? `${path}/${entry.name}` : entry.name;
                
                if (entry.kind === 'file') {
                    const extension = this.getFileExtension(entry.name).toLowerCase();
                    if (this.supportedFormats.includes(extension)) {
                        this.videoFiles.push({
                            name: entry.name,
                            path: currentPath,
                            handle: entry,
                            extension: extension
                        });
                    }
                } else if (entry.kind === 'directory') {
                    // Recursively scan subdirectories (limit depth to avoid infinite loops)
                    const depth = path.split('/').length;
                    if (depth < 3) { // Limit to 3 levels deep
                        await this.scanDirectory(entry, currentPath);
                    }
                }
            }
        } catch (error) {
            console.warn(`Error scanning directory ${path}:`, error);
        }
    }

    // Get file extension from filename
    getFileExtension(filename) {
        return filename.slice(filename.lastIndexOf('.'));
    }

    // Get a random selection of video files
    getRandomVideos(count) {
        if (this.videoFiles.length === 0) {
            throw new Error('No video files available');
        }

        const shuffled = [...this.videoFiles].sort(() => 0.5 - Math.random());
        return shuffled.slice(0, Math.min(count, this.videoFiles.length));
    }

    // Read a video file as ArrayBuffer
    async readVideoFile(videoFile) {
        try {
            const file = await videoFile.handle.getFile();
            return await file.arrayBuffer();
        } catch (error) {
            throw new Error(`Failed to read video file ${videoFile.name}: ${error.message}`);
        }
    }

    // Get video file metadata
    async getVideoMetadata(videoFile) {
        try {
            const file = await videoFile.handle.getFile();
            return {
                name: videoFile.name,
                size: file.size,
                lastModified: file.lastModified,
                type: file.type || 'video/mp4'
            };
        } catch (error) {
            console.warn(`Failed to get metadata for ${videoFile.name}:`, error);
            return {
                name: videoFile.name,
                size: 0,
                lastModified: Date.now(),
                type: 'video/mp4'
            };
        }
    }

    // Format file size for display
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Get folder info for display
    getFolderInfo() {
        if (!this.selectedFolder) {
            return null;
        }

        return {
            name: this.selectedFolder.name,
            videoCount: this.videoFiles.length,
            videos: this.videoFiles.map(video => ({
                name: video.name,
                path: video.path,
                extension: video.extension
            }))
        };
    }
}

// Export for use in other modules
window.FileManager = FileManager;
