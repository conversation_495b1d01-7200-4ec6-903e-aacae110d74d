# LazyRemixer Deployment Guide

## 🚀 Quick Start

### Docker Deployment (Recommended)

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd LazyRemixer
   ```

2. **Deploy with one command**
   ```bash
   ./deploy.sh
   ```
   
   Or manually:
   ```bash
   docker-compose up -d
   ```

3. **Access the application**
   Open your browser and go to: `http://localhost:3000`

## 📋 Requirements

- **Docker** and **Docker Compose** installed
- **Modern Browser**: Chrome 86+ or Edge 86+ (for File System Access API)

## 🔧 Docker Commands

### Basic Operations
```bash
# Start the application
docker-compose up -d

# Stop the application
docker-compose down

# View logs
docker-compose logs -f

# Restart the application
docker-compose restart

# Rebuild and start (after code changes)
docker-compose up -d --build
```

### Manual Docker Commands
```bash
# Build the image
docker build -t lazy-remixer .

# Run the container
docker run -p 3000:3000 lazy-remixer

# Run with environment variables
docker run -p 3000:3000 -e NODE_ENV=production lazy-remixer
```

## 🌐 Production Deployment

### Cloud Platforms

#### AWS ECS
```bash
# Build and tag for ECR
docker build -t lazy-remixer .
docker tag lazy-remixer:latest <account-id>.dkr.ecr.<region>.amazonaws.com/lazy-remixer:latest
docker push <account-id>.dkr.ecr.<region>.amazonaws.com/lazy-remixer:latest
```

#### Google Cloud Run
```bash
# Build and deploy
gcloud builds submit --tag gcr.io/<project-id>/lazy-remixer
gcloud run deploy --image gcr.io/<project-id>/lazy-remixer --platform managed
```

#### Azure Container Instances
```bash
# Build and push to ACR
docker build -t lazy-remixer .
docker tag lazy-remixer <registry-name>.azurecr.io/lazy-remixer:latest
docker push <registry-name>.azurecr.io/lazy-remixer:latest
```

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `NODE_ENV` | `development` | Environment mode |
| `PORT` | `3000` | Server port |

## 🔍 Health Checks

The Docker container includes built-in health checks:
- **Endpoint**: `http://localhost:3000`
- **Interval**: 30 seconds
- **Timeout**: 3 seconds
- **Retries**: 3

Check health status:
```bash
docker-compose ps
```

## 🛠 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Find process using port 3000
lsof -i :3000

# Kill the process
kill -9 <PID>

# Or use a different port
docker run -p 8080:3000 lazy-remixer
```

#### Container Won't Start
```bash
# Check logs
docker-compose logs lazy-remixer

# Check container status
docker-compose ps

# Rebuild the image
docker-compose up -d --build
```

#### File System Access API Not Working
- Ensure you're using Chrome 86+ or Edge 86+
- The API doesn't work in Firefox or Safari
- Must be served over HTTPS in production (localhost is exempt)

### Performance Optimization

#### For Large Video Files
- Increase Docker memory limits
- Consider using SSD storage
- Monitor CPU usage during video processing

#### Production Settings
```yaml
# docker-compose.prod.yml
services:
  lazy-remixer:
    build: .
    ports:
      - "80:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G
```

## 📊 Monitoring

### Container Logs
```bash
# Follow logs in real-time
docker-compose logs -f

# View last 100 lines
docker-compose logs --tail=100

# Filter by service
docker-compose logs lazy-remixer
```

### Resource Usage
```bash
# Monitor container stats
docker stats lazy-remixer-app

# Check disk usage
docker system df
```

## 🔒 Security

### Production Security Checklist
- [ ] Use HTTPS in production
- [ ] Set up proper firewall rules
- [ ] Use non-root user (already configured)
- [ ] Keep Docker images updated
- [ ] Monitor for security vulnerabilities
- [ ] Use secrets management for sensitive data

### Network Security
```yaml
# docker-compose.yml with custom network
services:
  lazy-remixer:
    build: .
    networks:
      - lazy-remixer-network
    ports:
      - "3000:3000"

networks:
  lazy-remixer-network:
    driver: bridge
```

## 📈 Scaling

### Horizontal Scaling
```yaml
# docker-compose.scale.yml
services:
  lazy-remixer:
    build: .
    deploy:
      replicas: 3
    ports:
      - "3000-3002:3000"
```

### Load Balancer (Nginx)
```nginx
upstream lazy-remixer {
    server localhost:3000;
    server localhost:3001;
    server localhost:3002;
}

server {
    listen 80;
    location / {
        proxy_pass http://lazy-remixer;
    }
}
```

## 🆘 Support

If you encounter issues:

1. Check the logs: `docker-compose logs -f`
2. Verify browser compatibility (Chrome/Edge 86+)
3. Ensure Docker and Docker Compose are up to date
4. Check available disk space and memory
5. Review the troubleshooting section above

For additional help, please check the main README.md file.
